from helper.setup_logger import setup_logger
logging = setup_logger("db_setup", "db_setup.log")
import mysql.connector
from mysql.connector import pooling
import threading
import os
from dotenv import load_dotenv

# Load .env variables into environment
load_dotenv()

# Read database credentials from .env
DB_HOST = os.getenv("DB_HOST")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_DATABASE")

# Global connection pool and lock
_connection_pool = None
_pool_lock = threading.Lock()

def setup_db_connection_pool():
    """Setup a MySQL connection pool using env variables."""
    global _connection_pool
    try:
        config = {
            'user': DB_USER,
            'password': DB_PASSWORD,
            'host': DB_HOST,
            'database': DB_NAME,
            'pool_name': 'mypool',
            'pool_size': 10,
            'pool_reset_session': True,
            'autocommit': True,
            'consume_results': True
        }
        _connection_pool = mysql.connector.pooling.MySQLConnectionPool(**config)
        print("✅ Database connection pool established")
        return _connection_pool
    except mysql.connector.Error as err:
        print(f"❌ Database connection pool failed: {err}")
        return None

def get_db_connection():
    """Get a connection from the pool, initializing the pool if necessary."""
    global _connection_pool
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                setup_db_connection_pool()
    
    try:
        if _connection_pool:
            conn = _connection_pool.get_connection()
            conn.autocommit = True
            return conn
    except mysql.connector.Error as err:
        print(f"❌ Error getting connection from pool: {err}")
    
    return None

def setup_db_connection():
    """Establish and return a single direct MySQL connection using env variables."""
    try:
        conn = mysql.connector.connect(
            host=DB_HOST, 
            user=DB_USER, 
            password=DB_PASSWORD, 
            database=DB_NAME,
            autocommit=True,
            consume_results=True
        )
        return conn
    except mysql.connector.Error as err:
        print(f"❌ Database connection failed: {err}")
        return None

# Initialize connection pool at startup
setup_db_connection_pool()

# Optional legacy direct connection for backward compatibility
CONN = setup_db_connection()












# import mysql.connector
# from mysql.connector import pooling
# import threading
# import os
# from dotenv import load_dotenv
# load_dotenv()

# # Global connection pool
# _connection_pool = None
# _pool_lock = threading.Lock()

# def setup_db_connection_pool(host: str, user: str, password: str, database: str):
#     """Setup a MySQL connection pool."""
#     global _connection_pool
#     try:
#         config = {
#             'user': user,
#             'password': password,
#             'host': host,
#             'database': database,
#             'pool_name': 'mypool',
#             'pool_size': 10,
#             'pool_reset_session': True,
#             'autocommit': True,
#             'consume_results': True
#         }
#         _connection_pool = mysql.connector.pooling.MySQLConnectionPool(**config)
#         print("✅ Database connection pool established")
#         return _connection_pool
#     except mysql.connector.Error as err:
#         print(f"❌ Database connection pool failed: {err}")
#         return None

# def get_db_connection():
#     """Get a connection from the pool."""
#     global _connection_pool
#     if _connection_pool is None:
#         with _pool_lock:
#             if _connection_pool is None:
#                 setup_db_connection_pool(
#                     host="localhost",
#                     user="root",
#                     password="test123",
#                     database="energy_db"
#                 )
    
#     try:
#         if _connection_pool:
#             conn = _connection_pool.get_connection()
#             conn.autocommit = True
#             return conn
#     except mysql.connector.Error as err:
#         print(f"❌ Error getting connection from pool: {err}")
    
#     return None

# def setup_db_connection(host: str, user: str, password: str, database: str):
#     """Establish and return a MySQL connection."""
#     try:
#         conn = mysql.connector.connect(
#             host=host, 
#             user=user, 
#             password=password, 
#             database=database,
#             autocommit=True,
#             consume_results=True
#         )
#         return conn
#     except mysql.connector.Error as err:
#         print(f"❌ Database connection failed: {err}")
#         return None





# ###Setup DB - Initialize the connection pool
# setup_db_connection_pool(
#     host="localhost",
#     user="root",
#     password="test123",
#     database="energy_db"
# )

# # Legacy single connection for backward compatibility
# CONN = setup_db_connection(
#     host="localhost",
#     user="root",
#     password="test123",
#     database="energy_db"
# )




