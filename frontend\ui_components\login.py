import streamlit as st
import hashlib
from datetime import datetime
import time
import os
import base64
from dotenv import load_dotenv
load_dotenv()
# -----------------------------
# CONFIG: Hardcoded single user
# -----------------------------

USERNAME = os.getenv("USERNAME_LOGIN")
PASSWORD = os.getenv("PASSWORD_LOGIN")
HASHED_PASSWORD = hashlib.sha256(PASSWORD.encode()).hexdigest()

# -----------------------------
# UTILS
# -----------------------------
def get_company_logo_base64():
    """Get base64 encoded company logo"""
    try:
        with open("logo/logo_integrum.jpg", "rb") as f:
            return base64.b64encode(f.read()).decode()
    except FileNotFoundError:
        # Fallback to a simple SVG logo if image not found
        return "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjNDI4NWY0Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+STwvdGV4dD4KPC9zdmc+"

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def authenticate_user(input_username: str, input_password: str) -> bool:
    return input_username == USERNAME and hash_password(input_password) == HASHED_PASSWORD

def initialize_session_state() -> None:
    st.session_state.setdefault('authenticated', False)
    st.session_state.setdefault('username', None)
    st.session_state.setdefault('login_time', None)

def is_authenticated() -> bool:
    return st.session_state.get('authenticated', False)

def get_current_user() -> str:
    return st.session_state.get('username')

def logout() -> None:
    with st.spinner("Signing out..."):
        time.sleep(0.5)
        st.session_state.authenticated = False
        st.session_state.username = None
        st.session_state.login_time = None
        st.success("👋 You have been signed out successfully!")
        time.sleep(1)
    st.rerun()

def login_form():
    apply_login_css()

    # Split layout: branding left, form right
    col_left, col_right = st.columns([1.2, 1])

    with col_left:
        # Get company logo
        logo_base64 = get_company_logo_base64()
        
        st.markdown(f"""
            <div class="branding-container">
                <div class="brand-header">
                    <div class="company-logo">
                        <img src="data:image/jpeg;base64,{logo_base64}" alt="Integrum Logo" />
                    </div>
                    <div class="company-name">
                        <h1>Integrum</h1>
                        <p class="tagline">Energy Management Solutions</p>
                    </div>
                </div>
                <div class="welcome-section">
                    <h2 class="welcome-title">Welcome Back</h2>
                    <p class="welcome-subtitle">Sign in to your Energy Dashboard</p>
                </div>
            </div>
        """, unsafe_allow_html=True)

    # with col_right:
    #     st.markdown("""
    #         <div class="form-container">
    #             <div class="form-content">
    #                 <div class="form-header">
    #                     <h2>Sign In</h2>
    #                     <p>Access your Energy Dashboard</p>
    #                 </div>
    #     """, unsafe_allow_html=True)

    #     with st.form("login_form"):
    #         st.markdown('<div class="form-group">', unsafe_allow_html=True)
    #         email = st.text_input("Email Address", placeholder="Email Address", key="email_input", label_visibility="collapsed")
    #         password = st.text_input("Password", placeholder="Password", type="password", key="password_input", label_visibility="collapsed")
            
            
    #         submit_btn = st.form_submit_button("Sign In to Dashboard", use_container_width=True)
    #         st.markdown('</div>', unsafe_allow_html=True)

    #     st.markdown("""
    #                 <div class="form-wrapper">
    #     """, unsafe_allow_html=True)
        
    #     if submit_btn:
    #         with st.spinner("Authenticating..."):
    #             time.sleep(0.5)
    #             if authenticate_user(email, password):
    #                 st.session_state.authenticated = True
    #                 st.session_state.username = email
    #                 st.session_state.login_time = datetime.now()
    #                 st.success("🎉 Authentication successful! Welcome to your dashboard.")
    #                 time.sleep(1)
    #                 st.rerun()
    #             else:
    #                 st.error("🔒 Authentication failed. Please check your credentials.")

    with col_right:
        # Simple text header without white box
        st.markdown("""
            <div style="text-align: center; padding: 2rem; margin-top: 2rem;">
                <h2 style="color: #333; margin-bottom: 0.5rem;">Sign In</h2>
                <p style="color: #666; margin-bottom: 2rem;">Access your Energy Dashboard</p>
            </div>
        """, unsafe_allow_html=True)

        with st.form("login_form"):
            email = st.text_input(
                "Email Address",
                placeholder="Email Address",
                key="email_input",
                label_visibility="collapsed"
            )
            password = st.text_input(
                "Password",
                placeholder="Password",
                type="password",
                key="password_input",
                label_visibility="collapsed"
            )
            submit_btn = st.form_submit_button("Sign In to Dashboard", use_container_width=True)

        # Submission logic
        if submit_btn:
            with st.spinner("Authenticating..."):
                time.sleep(0.5)
                if authenticate_user(email, password):
                    st.session_state.authenticated = True
                    st.session_state.username = email
                    st.session_state.login_time = datetime.now()
                    st.success("🎉 Authentication successful! Welcome to your dashboard.")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("🔒 Authentication failed. Please check your credentials.")


        
        





# -----------------------------
# UI: Sidebar
# -----------------------------
def add_logout_button() -> None:
    st.sidebar.markdown("""<div style="margin: 1rem 0;"></div>""", unsafe_allow_html=True)

    username = get_current_user()
    login_time = st.session_state.get('login_time')

    if username and login_time:
        session_duration = datetime.now() - login_time
        hours, remainder = divmod(session_duration.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        user_initial = username[0].upper()

        st.sidebar.markdown(f"""
            <div class="sidebar-user-info">
                <div class="user-profile-card">
                    <div class="user-avatar">{user_initial}</div>
                    <div class="user-details">
                        <div class="user-name">{username}</div>
                        <div class="user-login-time">
                            <i class="login-icon">🕐</i> Signed in at {login_time.strftime('%I:%M %p')}
                        </div>
                        <div class="session-status">
                            <div class="status-indicator"></div>
                            <span class="status-text">Active for {hours}h {minutes}m</span>
                        </div>
                    </div>
                </div>
            </div>
        """, unsafe_allow_html=True)

    if st.sidebar.button("🚪 Sign Out", use_container_width=True, key="logout_btn"):
        logout()

# -----------------------------
# UI: Authenticated Banner
# -----------------------------
def display_welcome_banner() -> None:
    username = get_current_user()
    login_time = st.session_state.get('login_time')
    current_time = datetime.now()
    time_diff = current_time - login_time

    hour = current_time.hour
    greeting = "Good morning" if hour < 12 else "Good afternoon" if hour < 17 else "Good evening"

    st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 24px rgba(66, 133, 244, 0.25);
            font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
            <h2 style="margin: 0 0 0.5rem 0; font-size: 1.5rem; font-weight: 400; display: flex; align-items: center; gap: 0.5rem;">
                👋 {greeting}, {username}!
            </h2>
            <p style="margin: 0; font-size: 1rem; opacity: 0.9; font-weight: 300;">
                Welcome back to your Energy Dashboard
            </p>
            <div style="display: flex; align-items: center; gap: 1rem; margin-top: 1rem; font-size: 0.9rem; opacity: 0.8;">
                <span>🕒 Session: {time_diff.seconds // 3600}h {(time_diff.seconds % 3600) // 60}m</span>
                <span>🔐 Status: Active</span>
                <span>📊 Dashboard: Ready</span>
            </div>
        </div>
    """, unsafe_allow_html=True)



def apply_login_css() -> None:
    st.markdown("""
    <style>
    body {
        background: #f8fafc;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .login-container {
        background: #ffffff;
        padding: 3rem;
        border-radius: 1.5rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-width: 400px;
        margin: auto;
        border: 1px solid #e2e8f0;
    }
    
    /* Branding Container */
    .branding-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 60vh;
        padding: 1.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        margin-right: 1rem;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .branding-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.1;
    }
    
    /* Brand Header */
    .brand-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 1;
    }
    .company-logo img {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        object-fit: cover;
        margin-bottom: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .company-name h1 {
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        margin: 0;
        text-align: center;
    }
    .tagline {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        margin: 0.25rem 0 0 0;
        text-align: center;
    }
    
    /* Welcome Section */
    .welcome-section {
        text-align: center;
        margin-top: 2rem;
        position: relative;
        z-index: 1;
    }
    
    .welcome-title {
        font-size: 2rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
    }
    
    .form-header {
        text-align: center;
        margin-bottom: 2rem;
        width: 100%;
    }
    
    .form-header h2 {
        font-size: 1.75rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.5rem 0;
    }
    
    .form-header p {
        font-size: 0.875rem;
        color: #64748b;
        margin: 0;
    }
    
    .form-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    /* Login Section */
    .login-section {
        text-align: center;
        margin-bottom: 2rem;
    }
    .login-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1e293b;
    }
    .login-subtitle {
        font-size: 0.875rem;
        color: #64748b;
        margin-bottom: 1rem;
    }
    /* Form Styling */
    .form-group {
        margin-bottom: 1.5rem;
        width: 100% !important;
        max-width: 400px !important;
    }
    
    .stTextInput {
        width: 100% !important;
        max-width: 400px !important;
    }
    
    .stTextInput>div {
        width: 100% !important;
        max-width: 400px !important;
    }
    
    .stTextInput>div>div {
        width: 100% !important;
        max-width: 400px !important;
    }
    
    .stTextInput>div>div>input {
        border-radius: 0.75rem !important;
        padding: 0.875rem 1rem !important;
        border: 1.5px solid #e2e8f0 !important;
        font-size: 0.875rem !important;
        transition: all 0.2s ease !important;
        background: #ffffff !important;
        width: 100% !important;
        max-width: 400px !important;
    }
    .stTextInput>div>div>input:focus {
        border-color: #4285f4;
        outline: none;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
    }
    .stCheckbox {
        margin: 0.5rem 0;
    }
    .stButton button {
        background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
        color: white;
        font-weight: 600;
        border-radius: 0.75rem;
        padding: 0.875rem 1.5rem;
        border: none;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .stButton button:hover {
        background: linear-gradient(135deg, #3367d6 0%, #1565c0 100%);
        transform: translateY(-1px);
        box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
    }
    .forgot-password {
        font-size: 0.875rem;
        color: #4285f4;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem 0;
        font-weight: 500;
    }
    .forgot-password:hover {
        text-decoration: underline;
    }
    /* Divider */
    .divider {
        text-align: center;
        margin: 2rem 0 1.5rem 0;
        position: relative;
    }
    .divider span {
        background: white;
        padding: 0 1rem;
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .divider::before, .divider::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 45%;
        height: 1px;
        background: #e2e8f0;
    }
    .divider::before { left: 0; }
    .divider::after { right: 0; }
    
    /* Social Buttons */
    .social-buttons {
        margin-bottom: 1.5rem;
    }
    .social-button {
        width: 100%;
        border: 1.5px solid #e2e8f0;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        margin-bottom: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        background: white;
        color: #374151;
    }
    .social-button:hover {
        border-color: #cbd5e1;
        background: #f8fafc;
        transform: translateY(-1px);
    }
    .google-button:hover {
        border-color: #4285f4;
        background: #f8fafc;
    }
    .microsoft-button:hover {
        border-color: #0078d4;
        background: #f8fafc;
    }
    
    /* Footer */
    .footer-text {
        text-align: center;
        margin-top: 1.5rem;
    }
    .footer-text p {
        margin: 0.5rem 0;
        font-size: 0.875rem;
        color: #64748b;
    }
    .signup-link {
        color: #4285f4;
        text-decoration: none;
        font-weight: 500;
    }
    .signup-link:hover {
        text-decoration: underline;
    }
    .terms-text {
        font-size: 0.75rem !important;
        color: #94a3b8 !important;
        margin-top: 1rem !important;
    }
    .terms-text a {
        color: #64748b;
        text-decoration: none;
    }
    .terms-text a:hover {
        text-decoration: underline;
    }
    
    /* Signup Text */
    .signup-text {
        text-align: center;
        margin-top: 1.5rem;
        font-size: 0.875rem;
        color: #64748b;
        width: 100%;
    }
    
    .signup-text a {
        color: #4285f4;
        text-decoration: none;
        font-weight: 500;
    }
    
    .signup-text a:hover {
        text-decoration: underline;
    }
    /* Right Panel */
    .right-panel {
        background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
        color: white;
        height: 100vh;
        padding: 3rem 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-radius: 1.5rem;
        position: relative;
        overflow: hidden;
    }
    .right-panel::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.1;
    }
    .right-content {
        position: relative;
        z-index: 1;
        max-width: 400px;
    }
    
    /* Hero Section */
    .hero-section {
        margin-bottom: 3rem;
    }
    .hero-icon {
        margin-bottom: 1.5rem;
    }
    .hero-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }
    .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        line-height: 1.5;
    }
    .features-list {
        display: grid;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    .feature-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.75rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .feature-icon {
        font-size: 1.25rem;
    }
    
    /* Testimonial */
    .testimonial {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 1rem;
        padding: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .testimonial blockquote {
        font-size: 0.95rem;
        font-style: italic;
        margin: 0 0 1rem 0;
        line-height: 1.5;
    }
    .testimonial-author {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    .author-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
    }
    .author-info {
        text-align: left;
    }
    .author-name {
        font-weight: 600;
        font-size: 0.875rem;
        margin: 0;
    }
    .author-title {
        font-size: 0.75rem;
        opacity: 0.8;
        margin: 0;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .branding-container {
            height: auto;
            margin-right: 0;
            margin-bottom: 1rem;
        }
        
        .form-container {
            height: auto;
            margin-left: 0;
            margin-top: 1rem;
        }
    }
    
    /* Streamlit specific adjustments */
    .stColumn {
        padding: 0 !important;
    }
    
    .stColumn > div {
        padding: 0 !important;
    }
    
    /* Remove default margins from streamlit */
    .stForm {
        background: transparent !important;
        border: none !important;
        padding: 0 !important;
    }
    
    .stCheckbox > label > div {
        font-size: 0.875rem !important;
    }
    
    </style>
    """, unsafe_allow_html=True)