# Solar & Wind Energy Generation Dashboard Information

## Summary
A comprehensive Streamlit-based dashboard for monitoring and analyzing solar and wind energy generation, consumption patterns, and settlement data. The application provides dynamic client and plant selection, flexible date filtering, and multiple analysis tabs for different aspects of energy data visualization.

## Structure
- **app.py**: Main Streamlit application entry point
- **frontend/**: UI components and display functions
- **backend/**: Data management and processing
- **db/**: Database connection and query functions
- **config/**: Application configuration settings
- **visualizations/**: Data visualization modules
- **helper/**: Utility functions and logging setup

## Language & Runtime
**Language**: Python
**Version**: Python 3.8 or higher
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- streamlit>=1.28.0: Web application framework
- pandas>=1.5.0: Data manipulation and analysis
- mysql-connector-python>=8.0.0: MySQL database connector
- plotly>=5.0.0: Interactive visualization library
- numpy>=1.21.0: Numerical computing

**Development Dependencies**:
- datetime: Date and time manipulation
- logging: Logging functionality

## Database Configuration
**Type**: MySQL
**Connection Settings**:
```python
host="localhost"
user="root"
password="test123"
database="energy_db"
```

**Schema Requirements**:
- settlement_data table with fields for client_name, date, datetime, allocated_generation, consumption, deficit, surplus_demand, surplus_generation, settled

## Build & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Configure database connection
# Update credentials in db/db_setup.py

# Run the dashboard
streamlit run app.py
```

## Application Structure
**Main Entry Point**: app.py
**UI Components**: 
- Dashboard controls (client/plant selection, date filters)
- Three analysis tabs: Summary, ToD Analysis, Power Cost Analysis

**Data Flow**:
1. User selects client, plant, and date range
2. Application fetches data from MySQL database
3. Data is processed and visualized in appropriate tabs
4. Interactive plots display energy generation and consumption patterns

## Features
- Client and plant selection with mutual exclusion logic
- Date range filtering with single-day and multi-day analysis
- Summary tab with generation vs consumption analysis
- ToD (Time of Day) tab with time-based analysis
- Power Cost Analysis tab (coming soon)
- Modern, responsive design with custom CSS styling

## Configuration
**Application Settings**: config/app_config.py
- UI colors and themes
- Feature flags
- Error messages
- Date range limits

**Database Settings**: db/db_setup.py
- Connection parameters
- Connection pooling configuration
- Error handling and retry logic

## Testing
No formal testing framework was identified in the repository. The application relies on manual testing and error handling through try-except blocks and logging.