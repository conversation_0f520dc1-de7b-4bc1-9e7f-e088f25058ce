
import streamlit as st
from datetime import datetime, timedelta
from db.db_setup import CONN
from db.fetch_summary_data import (
    fetch_generation_consumption_data,
    fetch_unitwise_consumption_and_generation,
    fetch_unitwise_consumption_generation_with_location,
    fetch_generation_consumption_daily_hourly
)
from db.fetch_tod_tab_data import fetch_tod_data_with_granularity
from visualizations.summary_tab_visual import (
    plot_generation_vs_consumption,
    plot_generation_vs_consumption_interactive,
    create_generation_only_plot, create_consumption_plot,
    plot_consumption_and_generation_pie,
    plot_consumption_and_generation_pie_interactive,
    create_merged_consumption_generation_table,
    plot_generation_vs_consumption_hourly,
    plot_generation_vs_consumption_hourly_interactive,
    plot_tod_generation_consumption_lines
)
from visualizations.unit_wise_power_cost_calculations import (
    fetch_unitwise_monthly_data,
    calculate_unitwise_monthly_power_costs,
    create_unitwise_monthly_bill_table,
    summarize_unitwise_costs_table,
    plot_unitwise_grid_vs_actual_cost_bar_chart,
    plot_unitwise_monthly_savings_heatmap,
    plot_single_unit_cost_timeseries,
    plot_single_unit_savings_trend
)
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting
from helper.setup_logger import setup_logger



logging = setup_logger("summary_display", "summary_display.log")


def display_generation_vs_consumption(selected_plant, start_date, end_date=None, is_hourly_aggregation=False):
    """Display generation vs consumption chart with metrics."""

    # Set start date to 12 months ago and end date to current date
    try:
        current_date = datetime.now()
        start_date_12_months_ago = current_date - timedelta(days=365)  # Approximately 12 months

        # Override the provided dates with our calculated dates
        start_date = start_date_12_months_ago
        end_date = current_date

        # Convert dates to string format
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

    except Exception as e:
        logging.error(f"Error setting up date range: {str(e)}")
        st.error("There was an issue setting up the date range. Please try again.")
        return
   
    logging.info(f"Displaying generation vs consumption for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}")
    
    # Get universal plot options setting
    try:
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Interactive plot setting: {use_interactive}")
    except Exception as e:
        logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
        use_interactive = False

    try:
        # Fetch data for chart
        df = fetch_generation_consumption_daily_hourly(CONN, selected_plant, start_date_str, end_date_str, hourly_aggregation=is_hourly_aggregation)
        
        if df is not None and not df.empty:
            logging.info(f"Successfully fetched chart data with {len(df)} records")

            # Generate chart
            try:
                if use_interactive:
                    # Use the interactive Plotly version
                    fig = plot_generation_vs_consumption_interactive(
                        df=df,
                        plant_display_name=selected_plant,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        is_hourly_aggregation=is_hourly_aggregation
                    )
                    if fig:
                        st.plotly_chart(fig, use_container_width=True)
                        logging.info("Successfully displayed interactive chart")
                    else:
                        logging.warning("Interactive chart generation returned None")
                        st.warning("⚠️ Unable to generate the interactive chart. Please try refreshing the page.")
                else:
                    # Use the original matplotlib version
                    fig = plot_generation_vs_consumption(
                        df=df,
                        plant_display_name=selected_plant,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        is_hourly_aggregation=is_hourly_aggregation
                    )
                    if fig:
                        st.pyplot(fig)
                        logging.info("Successfully displayed static chart")
                    else:
                        logging.warning("Static chart generation returned None")
                        st.warning("⚠️ Unable to generate the chart. Please try refreshing the page.")
            except Exception as e:
                logging.error(f"Error generating chart: {str(e)}")
                st.error("There was an issue creating the chart. Please try again or contact support.")

            # Fetch data for metrics
            try:
                df_metrics = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
                
                if df_metrics is not None and not df_metrics.empty:
                    logging.info(f"Successfully fetched metrics data with {len(df_metrics)} records")
                    
                    # Calculate metrics
                    total_generation_kwh = df_metrics['generation'].sum()
                    total_consumption_kwh = df_metrics['consumption'].sum()
                    total_settled_kwh = df_metrics['settled'].sum()
                    total_surplus_demand_kwh = df_metrics['surplus_demand'].sum()
                    
                    # Convert to MWh
                    total_generation_mwh = total_generation_kwh / 1000
                    total_consumption_mwh = total_consumption_kwh / 1000
                    total_settled_mwh = total_settled_kwh / 1000
                    surplus_demand_mwh = total_surplus_demand_kwh / 1000
                    
                    # Calculate generation after loss (2.8% loss)
                    loss_percentage = 2.8
                    total_generation_after_loss_mwh = total_generation_mwh * (1 - loss_percentage / 100)
                    
                    # Calculate replacement percentage (without Banking)
                    replacement_percentage = (total_settled_mwh / total_consumption_mwh * 100) if total_consumption_mwh > 0 else 0
                    
                    logging.info(f"Calculated metrics - Generation: {total_generation_mwh:.2f} MWh, Consumption: {total_consumption_mwh:.2f} MWh")
                    
                    # Add CSS to style metric containers
                    st.markdown("""
                    <style>
                    [data-testid="metric-container"] [data-testid="metric-container-label"] {
                        font-size: 0.9em;
                        font-weight: bold;
                    }
                    [data-testid="metric-container"] [data-testid="metric-container-value"] {
                        font-size: 0.7em;
                    }
                    </style>
                    """, unsafe_allow_html=True)
                    
                    # Create 5 horizontal boxes with main metrics using Streamlit columns
                    col1, col2, col3, col4, col5 = st.columns(5)
                    with col1:
                        st.metric(
                            label="Total Generation (MWh)",
                            value=f"{total_generation_mwh:.2f}"
                        )
                    with col2:
                        st.metric(
                            label="Generation (after loss)",
                            value=f"{total_generation_after_loss_mwh:.2f}",
                            help=f"Generation after {loss_percentage}% transmission/distribution loss"
                        )
                    with col3:
                        st.metric(
                            label="Total Consumption (MWh)",
                            value=f"{total_consumption_mwh:.2f}"
                        )
                    with col4:
                        st.metric(
                            label="Replacement (without Banking) %",
                            value=f"{replacement_percentage:.2f}%",
                            help="Percentage of replacement (without Banking)"
                        )
                    with col5:
                        st.metric(
                            label="Surplus Demand (MWh)",
                            value=f"{surplus_demand_mwh:.2f}",
                            help="Surplus demand (without banking)"
                        )
                else:
                    logging.warning(f"No metrics data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
                    st.warning("⚠️ Metrics data is not available for the selected period.")
            except Exception as e:
                logging.error(f"Error calculating metrics: {str(e)}")
                st.error("There was an issue calculating the energy metrics. Chart is displayed without metrics.")
            
        else:
            logging.warning(f"No chart data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
            st.warning("⚠️ No data available for the selected plant and date range. Please try a different date range or plant.")
    except Exception as e:
        logging.error(f"Error in display_generation_vs_consumption: {str(e)}")
        st.error("We're experiencing technical difficulties loading the energy data. Please try again later or contact support if the issue persists.")





def display_generation_vs_consumption_hourly(selected_plant, start_date, end_date=None):
    """Display hourly generation vs consumption chart."""
    
    # Convert dates to string format if they're date objects
    try:
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y-%m-%d')
        else:
            start_date_str = str(start_date)
        
        if end_date is None:
            end_date_str = start_date_str
        elif hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            end_date_str = str(end_date)
    except Exception as e:
        logging.error(f"Error converting dates to string format: {str(e)}")
        st.error("There was an issue processing the selected dates. Please try again.")
        return
   
    # Determine if we should use hourly aggregation
    is_single_day = start_date_str == end_date_str
    use_hourly_aggregation = not is_single_day  # Use hourly aggregation for multi-day selections
    
    logging.info(f"Displaying hourly generation vs consumption for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}, hourly_aggregation: {use_hourly_aggregation}")
    
    # Get universal plot options setting
    try:
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Interactive plot setting: {use_interactive}")
    except Exception as e:
        logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
        use_interactive = False
    
    try:
        df = fetch_generation_consumption_data(
            CONN, 
            selected_plant, 
            start_date_str, 
            end_date_str, 
            hourly_aggregation=use_hourly_aggregation
        )

        if df is not None and not df.empty:
            logging.info(f"Successfully fetched hourly data with {len(df)} records")
            
            try:
                if use_interactive:
                    # Use the interactive Plotly version
                    fig = plot_generation_vs_consumption_hourly_interactive(
                        df=df,
                        plant_display_name=selected_plant,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        is_hourly_aggregation=use_hourly_aggregation
                    )
                    if fig:
                        st.plotly_chart(fig, use_container_width=True)
                        logging.info("Successfully displayed interactive hourly chart")
                    else:
                        logging.warning("Interactive hourly chart generation returned None")
                        st.warning("⚠️ Unable to generate the interactive hourly chart. Please try refreshing the page.")
                else:
                    # Use the original matplotlib version
                    fig = plot_generation_vs_consumption_hourly(
                        df=df,
                        plant_display_name=selected_plant,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        is_hourly_aggregation=use_hourly_aggregation
                    )
                    if fig:
                        st.pyplot(fig)
                        logging.info("Successfully displayed static hourly chart")
                    else:
                        logging.warning("Static hourly chart generation returned None")
                        st.warning("⚠️ Unable to generate the hourly chart. Please try refreshing the page.")
            except Exception as e:
                logging.error(f"Error generating hourly chart: {str(e)}")
                st.error("There was an issue creating the hourly chart. Please try again or contact support.")
            
        else:
            logging.warning(f"No hourly data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
            st.warning("⚠️ No hourly data available for the selected plant and date range. Please try a different date range or plant.")
    except Exception as e:
        logging.error(f"Error in display_generation_vs_consumption_hourly: {str(e)}")
        st.error("We're experiencing technical difficulties loading the hourly energy data. Please try again later or contact support if the issue persists.")


    
def display_generation_only(selected_plant, start_date, end_date=None):
    """Display generation only chart."""
    
    # Convert dates to string format if they're date objects
    try:
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y-%m-%d')
        else:
            start_date_str = str(start_date)
        
        if end_date is None:
            end_date_str = start_date_str
        elif hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            end_date_str = str(end_date)
    except Exception as e:
        logging.error(f"Error converting dates to string format: {str(e)}")
        st.error("There was an issue processing the selected dates. Please try again.")
        return

    logging.info(f"Displaying generation only for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}")

    try:
        df = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
        
        if df is not None and not df.empty:
            logging.info(f"Successfully fetched generation data with {len(df)} records")
            
            try:
                fig = create_generation_only_plot(
                    df=df,
                    plant_name=selected_plant,
                    start_date=start_date_str,
                    end_date=end_date_str
                )
                if fig:
                    st.pyplot(fig)
                    logging.info("Successfully displayed generation only chart")
                else:
                    logging.warning("Generation only chart generation returned None")
                    st.warning("⚠️ Unable to generate the generation chart. Please try refreshing the page.")
            except Exception as e:
                logging.error(f"Error generating generation only chart: {str(e)}")
                st.error("There was an issue creating the generation chart. Please try again or contact support.")
        else:
            logging.warning(f"No generation data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
            st.warning("⚠️ No generation data available for the selected plant and date range. Please try a different date range or plant.")
    except Exception as e:
        logging.error(f"Error in display_generation_only: {str(e)}")
        st.error("We're experiencing technical difficulties loading the generation data. Please try again later or contact support if the issue persists.")




def display_consumption_only(selected_plant, start_date, end_date=None):
    """Display consumption only chart."""
    
    # Convert dates to string format if they're date objects
    try:
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y-%m-%d')
        else:
            start_date_str = str(start_date)
        
        if end_date is None:
            end_date_str = start_date_str
        elif hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            end_date_str = str(end_date)
    except Exception as e:
        logging.error(f"Error converting dates to string format: {str(e)}")
        st.error("There was an issue processing the selected dates. Please try again.")
        return

    logging.info(f"Displaying consumption only for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}")

    try:
        df = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
        
        if df is not None and not df.empty:
            logging.info(f"Successfully fetched consumption data with {len(df)} records")
            
            try:
                fig = create_consumption_plot(
                    df=df,
                    plant_name=selected_plant,
                    start_date=start_date_str,
                    end_date=end_date_str
                )
                if fig:
                    st.pyplot(fig)
                    logging.info("Successfully displayed consumption only chart")
                else:
                    logging.warning("Consumption only chart generation returned None")
                    st.warning("⚠️ Unable to generate the consumption chart. Please try refreshing the page.")
            except Exception as e:
                logging.error(f"Error generating consumption only chart: {str(e)}")
                st.error("There was an issue creating the consumption chart. Please try again or contact support.")
        else:
            logging.warning(f"No consumption data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
            st.warning("⚠️ No consumption data available for the selected plant and date range. Please try a different date range or plant.")
    except Exception as e:
        logging.error(f"Error in display_consumption_only: {str(e)}")
        st.error("We're experiencing technical difficulties loading the consumption data. Please try again later or contact support if the issue persists.")







def display_consumption_and_generation_pie(selected_plant, start_date, end_date=None):
    """Display pie chart for consumption and generation breakdown by units."""
    
    # Convert dates to string format if they're date objects
    try:
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y-%m-%d')
        else:
            start_date_str = str(start_date)
        
        if end_date is None:
            end_date_str = start_date_str
        elif hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            end_date_str = str(end_date)
    except Exception as e:
        logging.error(f"Error converting dates to string format: {str(e)}")
        st.error("There was an issue processing the selected dates. Please try again.")
        return

    logging.info(f"Displaying consumption and generation pie chart for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}")

    

    try:
        # Fetch data with location information
        df_with_location = fetch_unitwise_consumption_generation_with_location(
            CONN,
            selected_plant,
            start_date_str,
            end_date_str
        )

        if df_with_location is not None and not df_with_location.empty:
            logging.info(f"Successfully fetched unitwise data with location, {len(df_with_location)} records")
            
            # Get universal plot options setting
            try:
                use_interactive = get_interactive_plot_setting()
                logging.info(f"Interactive plot setting: {use_interactive}")
            except Exception as e:
                logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
                use_interactive = False
            
            # Fetch data for pie chart
            try:
                df_for_pie = fetch_unitwise_consumption_and_generation(
                    CONN,
                    selected_plant,
                    start_date_str,
                    end_date_str
                )
                
                if df_for_pie is not None and not df_for_pie.empty:
                    logging.info(f"Successfully fetched pie chart data with {len(df_for_pie)} records")
                    
                    try:
                        if use_interactive:
                            # Use the interactive Plotly version
                            fig = plot_consumption_and_generation_pie_interactive(
                                df=df_for_pie,
                                plant_display_name=selected_plant,
                                start_date=start_date_str,
                                end_date=end_date_str
                            )
                            if fig:
                                st.plotly_chart(fig, use_container_width=True)
                                logging.info("Successfully displayed interactive pie chart")
                            else:
                                logging.warning("Interactive pie chart generation returned None")
                                st.warning("⚠️ Unable to generate the interactive pie chart. Please try refreshing the page.")
                        else:
                            # Use the original matplotlib version
                            fig = plot_consumption_and_generation_pie(df_for_pie, selected_plant, start_date_str, end_date_str)
                            if fig:
                                st.pyplot(fig)
                                logging.info("Successfully displayed static pie chart")
                            else:
                                logging.warning("Static pie chart generation returned None")
                                st.warning("⚠️ Unable to generate the pie chart. Please try refreshing the page.")
                    except Exception as e:
                        logging.error(f"Error generating pie chart: {str(e)}")
                        st.error("There was an issue creating the pie chart. Please try again or contact support.")
                else:
                    logging.warning(f"No pie chart data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
                    st.warning("⚠️ No pie chart data available for the selected period.")
            except Exception as e:
                logging.error(f"Error fetching pie chart data: {str(e)}")
                st.error("There was an issue loading the pie chart data. Please try again or contact support.")
            
            # Display the new merged table
            st.markdown("### 📋 Consumption & Generation summary by consumption unit")
            
            try:
                # Create the merged table
                merged_table = create_merged_consumption_generation_table(df_with_location, selected_plant)
                
                if not merged_table.empty:
                    # Display the merged table
                    st.dataframe(merged_table, use_container_width=True, hide_index=True)
                    logging.info(f"Successfully displayed merged table with {len(merged_table)} rows")
                else:
                    logging.warning("Merged table is empty")
                    st.warning("⚠️ No summary table data available for the selected period.")
            except Exception as e:
                logging.error(f"Error creating merged table: {str(e)}")
                st.error("There was an issue creating the summary table. Please try again or contact support.")



        else:
            logging.warning(f"No unitwise data available for plant {selected_plant} between {start_date_str} and {end_date_str}")
            st.warning("⚠️ No unitwise data available for the selected plant and date range. Please try a different date range or plant.")
    except Exception as e:
        logging.error(f"Error in display_consumption_and_generation_pie: {str(e)}")
        st.error("We're experiencing technical difficulties loading the unitwise consumption and generation data. Please try again later or contact support if the issue persists.")


def display_unitwise_monthly_bill_analysis(selected_plant, start_date, end_date=None, grid_rate=4.0, renewable_rate=2.0):
    """Display unit-wise monthly bill analysis with grid power cost and after adjustment."""

    # Convert dates to string format if they're date objects
    try:
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y-%m-%d')
        else:
            start_date_str = str(start_date)

        if end_date is None:
            end_date_str = start_date_str
        elif hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            end_date_str = str(end_date)
    except Exception as e:
        logging.error(f"Error converting dates to string format: {str(e)}")
        st.error("There was an issue processing the selected dates. Please try again.")
        return

    logging.info(f"Displaying unit-wise monthly bill analysis for plant: {selected_plant}, dates: {start_date_str} to {end_date_str}")

    # Display unit-wise monthly bill table

    try:
        # Fetch unit-wise monthly data
        unitwise_monthly_data = fetch_unitwise_monthly_data(CONN, selected_plant)

        if not unitwise_monthly_data.empty:
            # Calculate unit-wise monthly power costs
            unitwise_costs = calculate_unitwise_monthly_power_costs(unitwise_monthly_data, grid_rate, renewable_rate)

            if not unitwise_costs.empty:
                # Display the plots first
              
                display_unitwise_monthly_bill_plots(selected_plant, grid_rate, renewable_rate)
                
                st.markdown("---")
                
                # Display the summary table
                st.subheader("📋 Unit-wise Cost Summary Table")
                summary_table = summarize_unitwise_costs_table(unitwise_costs)
                if not summary_table.empty:
                    st.dataframe(summary_table, use_container_width=True, hide_index=True)
                    logging.info(f"Successfully displayed unit-wise summary table with {len(summary_table)} rows")
                else:
                    st.warning("⚠️ No summary data available.")
            else:
                st.warning("⚠️ No cost calculation data available for the selected period.")
        else:
            st.warning("⚠️ No unit-wise monthly data available for the selected period.")
    except Exception as e:
        logging.error(f"Error creating unit-wise monthly bill table: {str(e)}")
        st.error("There was an issue creating the unit-wise monthly bill table. Please try again or contact support.")


def display_unitwise_monthly_bill_plots(selected_plant, grid_rate=4.0, renewable_rate=2.0):
    """
    Display unit-wise monthly bill analysis plots with dropdown for single unit view.
    
    Args:
        selected_plant (str): Selected plant/client name
        grid_rate (float): Grid rate per kWh
        renewable_rate (float): Renewable rate per kWh
    """
    try:
        logging.info(f"Displaying unit-wise monthly bill plots for plant: {selected_plant}")
        
        # Get universal plot options setting
        try:
            use_interactive = get_interactive_plot_setting()
            logging.info(f"Interactive plot setting: {use_interactive}")
        except Exception as e:
            logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
            use_interactive = False
        
        # Fetch unit-wise monthly data
        unitwise_monthly_data = fetch_unitwise_monthly_data(CONN, selected_plant)
        
        if not unitwise_monthly_data.empty:
            # Calculate unit-wise monthly power costs
            unitwise_costs = calculate_unitwise_monthly_power_costs(unitwise_monthly_data, grid_rate, renewable_rate)
            
            if not unitwise_costs.empty:
                logging.info(f"Successfully calculated unit-wise costs with {len(unitwise_costs)} records")
                
                # Get unique units for dropdown
                available_units = sorted(unitwise_costs['Unit'].unique())
                
                # Add dropdown for unit selection
                
                view_options = ["All Units"] + available_units
                selected_view = st.selectbox(
                    "Choose view type:",
                    view_options,
                    index=0,
                    key="unit_view_selector",
                    help="Select 'All Units' for comparison view or a specific unit for detailed trend analysis"
                )
                
                st.markdown("---")
                
                if selected_view == "All Units":
                    # Multi-unit view: Show grouped bar chart and heatmap
                    
                    # Plot 1: Grid Cost vs Actual Cost Bar Chart
                    st.subheader("💰 Grid Cost vs Actual Cost - All Units")
                    try:
                        fig_bar = plot_unitwise_grid_vs_actual_cost_bar_chart(
                            df=unitwise_costs,
                            client_name=selected_plant,
                            use_interactive=use_interactive
                        )
                        
                        if fig_bar:
                            if use_interactive:
                                st.plotly_chart(fig_bar, use_container_width=True)
                            else:
                                st.pyplot(fig_bar)
                            logging.info("Successfully displayed unit-wise bar chart")
                        else:
                            st.warning("⚠️ Unable to generate the bar chart. Please try refreshing the page.")
                    except Exception as e:
                        logging.error(f"Error creating unit-wise bar chart: {str(e)}")
                        st.error("There was an issue creating the bar chart. Please try again or contact support.")
                    
                    st.markdown("---")
                    
                    # Plot 2: Monthly Savings Heatmap
                    st.subheader("🔥 Monthly Savings Heatmap - All Units")
                    try:
                        fig_heatmap = plot_unitwise_monthly_savings_heatmap(
                            df=unitwise_costs,
                            client_name=selected_plant,
                            use_interactive=use_interactive
                        )
                        
                        if fig_heatmap:
                            if use_interactive:
                                st.plotly_chart(fig_heatmap, use_container_width=True)
                            else:
                                st.pyplot(fig_heatmap)
                            logging.info("Successfully displayed unit-wise heatmap")
                        else:
                            st.warning("⚠️ Unable to generate the heatmap. Please try refreshing the page.")
                    except Exception as e:
                        logging.error(f"Error creating unit-wise heatmap: {str(e)}")
                        st.error("There was an issue creating the heatmap. Please try again or contact support.")
                
                else:
                    # Single unit view: Show time series charts
                    unit_data = unitwise_costs[unitwise_costs['Unit'] == selected_view]
                    
                    if not unit_data.empty:
                        # Plot 1: Grid Cost vs Actual Cost Time Series
                        st.subheader(f"💰 Cost Trend Over Time - {selected_view}")
                        try:
                            fig_timeseries = plot_single_unit_cost_timeseries(
                                df=unit_data,
                                unit_name=selected_view,
                                client_name=selected_plant,
                                use_interactive=use_interactive
                            )
                            
                            if fig_timeseries:
                                if use_interactive:
                                    st.plotly_chart(fig_timeseries, use_container_width=True)
                                else:
                                    st.pyplot(fig_timeseries)
                                logging.info(f"Successfully displayed time series chart for {selected_view}")
                            else:
                                st.warning("⚠️ Unable to generate the time series chart. Please try refreshing the page.")
                        except Exception as e:
                            logging.error(f"Error creating time series chart for {selected_view}: {str(e)}")
                            st.error("There was an issue creating the time series chart. Please try again or contact support.")
                        
                        st.markdown("---")
                        
                        # Plot 2: Savings Trend Line Chart
                        st.subheader(f"📈 Savings Trend - {selected_view}")
                        try:
                            fig_savings_trend = plot_single_unit_savings_trend(
                                df=unit_data,
                                unit_name=selected_view,
                                client_name=selected_plant,
                                use_interactive=use_interactive
                            )
                            
                            if fig_savings_trend:
                                if use_interactive:
                                    st.plotly_chart(fig_savings_trend, use_container_width=True)
                                else:
                                    st.pyplot(fig_savings_trend)
                                logging.info(f"Successfully displayed savings trend chart for {selected_view}")
                            else:
                                st.warning("⚠️ Unable to generate the savings trend chart. Please try refreshing the page.")
                        except Exception as e:
                            logging.error(f"Error creating savings trend chart for {selected_view}: {str(e)}")
                            st.error("There was an issue creating the savings trend chart. Please try again or contact support.")
                    else:
                        st.warning(f"⚠️ No data available for unit: {selected_view}")
                
                # Add some key insights below the plots
                
                try:
                    # Calculate overall metrics
                    total_grid_cost = unitwise_costs['Grid Cost (₹)'].sum()
                    total_actual_cost = unitwise_costs['Actual Cost (₹)'].sum()
                    total_savings = unitwise_costs['Savings (₹)'].sum()
                    overall_savings_pct = (total_savings / total_grid_cost * 100) if total_grid_cost > 0 else 0
                    
                    # Calculate business metrics
                    unit_summary = unitwise_costs.groupby('Unit').agg({
                        'Grid Cost (₹)': 'sum',
                        'Actual Cost (₹)': 'sum',
                        'Savings (₹)': 'sum'
                    }).reset_index()
                    unit_summary['Savings (%)'] = unit_summary.apply(
                        lambda row: (row['Savings (₹)'] / row['Grid Cost (₹)'] * 100) if row['Grid Cost (₹)'] > 0 else 0,
                        axis=1
                    )
                    
                    logging.info("Successfully displayed unit-wise insights")
                    
                except Exception as e:
                    logging.error(f"Error calculating unit-wise insights: {str(e)}")
                    st.warning("⚠️ Unable to calculate insights. Chart data is displayed without metrics.")
                    
            else:
                st.warning("⚠️ No cost calculation data available for the selected period.")
        else:
            st.warning("⚠️ No unit-wise monthly data available for the selected period.")
            
    except Exception as e:
        logging.error(f"Error in display_unitwise_monthly_bill_plots: {str(e)}")
        st.error("We're experiencing technical difficulties loading the unit-wise analysis. Please try again later or contact support if the issue persists.")


def display_tod_generation_consumption_lines(selected_plant, start_date, end_date=None):
    """
    Display ToD-based generation and consumption line chart with granularity options.
    
    Args:
        selected_plant (str): Selected plant/client name
        start_date: Start date (string or date object)
        end_date: End date (string or date object, optional)
    """
    try:
        logging.info(f"Displaying ToD generation vs consumption lines for plant: {selected_plant}")
        
        # Convert dates to string format if they're date objects
        try:
            if hasattr(start_date, 'strftime'):
                start_date_str = start_date.strftime('%Y-%m-%d')
            else:
                start_date_str = str(start_date)
            
            if end_date is None:
                end_date_str = start_date_str
            elif hasattr(end_date, 'strftime'):
                end_date_str = end_date.strftime('%Y-%m-%d')
            else:
                end_date_str = str(end_date)
        except Exception as e:
            logging.error(f"Error converting dates to string format: {str(e)}")
            st.error("There was an issue processing the selected dates. Please try again.")
            return
        
        # Get universal plot options setting
        try:
            use_interactive = get_interactive_plot_setting()
            logging.info(f"Interactive plot setting: {use_interactive}")
        except Exception as e:
            logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
            use_interactive = False
        
        
        
        # Create columns for controls
        col1, col2 = st.columns([1, 3])
        
        with col1:
            granularity = st.selectbox(
                "Select Granularity:",
                options=["daily", "60min", "15min"],
                index=0,
                help="Choose the time granularity for the analysis:\n"
                     "• Daily: Aggregated by day\n"
                     "• 60min: Aggregated by hour\n"
                     "• 15min: 15-minute intervals"
            )
        
        
        
        # Fetch ToD data with selected granularity
        try:
            df = fetch_tod_data_with_granularity(
                CONN,
                selected_plant,
                start_date_str,
                end_date_str,
                granularity
            )
            
            if df is not None and not df.empty:
                logging.info(f"Successfully fetched ToD data with {len(df)} records for {granularity} granularity")
                
                # Add some metrics before the chart
                try:
                    # Calculate total values by slot
                    slot_totals = df.groupby('slot').agg({
                        'generation_kwh': 'sum',
                        'consumption_kwh': 'sum'
                    }).reset_index()
                    slot_totals['surplus_generation'] = slot_totals['generation_kwh'] - slot_totals['consumption_kwh']
                    slot_totals['surplus_generation'] = slot_totals['surplus_generation'].apply(lambda x: max(0, x))
                    slot_totals['surplus_demand'] = slot_totals['consumption_kwh'] - slot_totals['generation_kwh']
                    slot_totals['surplus_demand'] = slot_totals['surplus_demand'].apply(lambda x: max(0, x))
                    
                    
                    
                except Exception as e:
                    logging.warning(f"Error calculating ToD metrics: {str(e)}")
                    # Continue without metrics
                
                # Create the line chart
                try:
                    fig = plot_tod_generation_consumption_lines(
                        df=df,
                        plant_display_name=selected_plant,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        is_interactive=use_interactive
                    )
                    
                    if fig:
                        if use_interactive:
                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.pyplot(fig)
                        logging.info("Successfully displayed ToD line chart")
                        
                       
                        
                    else:
                        st.warning("⚠️ Unable to generate the ToD line chart. Please try refreshing the page.")
                except Exception as e:
                    logging.error(f"Error generating ToD line chart: {str(e)}")
                    st.error("There was an issue creating the ToD line chart. Please try again or contact support.")
            else:
                st.warning(f"⚠️ No ToD data available for the selected plant and date range with {granularity} granularity. Please try a different date range or granularity.")
                
        except Exception as e:
            logging.error(f"Error fetching ToD data: {str(e)}")
            st.error("There was an issue fetching the ToD data. Please try again or contact support.")
            
    except Exception as e:
        logging.error(f"Error in display_tod_generation_consumption_lines: {str(e)}")
        st.error("We're experiencing technical difficulties loading the ToD analysis. Please try again later or contact support if the issue persists.")

