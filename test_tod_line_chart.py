#!/usr/bin/env python3
"""
Test script for ToD line chart functionality
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.db_setup import get_db_connection
from db.fetch_tod_tab_data import fetch_tod_data_with_granularity
from visualizations.summary_tab_visual import plot_tod_generation_consumption_lines

def test_tod_line_chart():
    """Test the ToD line chart functionality"""
    
    print("🔍 Testing ToD Line Chart Functionality...")
    
    # Test database connection
    try:
        conn = get_db_connection()
        if conn is None:
            print("❌ Database connection failed")
            return
        print("✅ Database connection successful")
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return
    
    # Test data fetching
    try:
        # You might need to adjust these values based on your actual data
        client_name = "test_client"  # Replace with actual client name
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        print(f"📊 Testing data fetch for client: {client_name}")
        
        # Test different granularities
        granularities = ["daily", "60min", "15min"]
        
        for granularity in granularities:
            print(f"\n🔄 Testing {granularity} granularity...")
            
            df = fetch_tod_data_with_granularity(
                conn, client_name, start_date, end_date, granularity
            )
            
            print(f"📈 {granularity} data shape: {df.shape}")
            
            if not df.empty:
                print(f"📋 Columns: {list(df.columns)}")
                print(f"📅 Date range: {df.iloc[0]['date' if granularity == 'daily' else 'datetime']} to {df.iloc[-1]['date' if granularity == 'daily' else 'datetime']}")
                print(f"🎯 Unique slots: {df['slot'].unique()}")
                print(f"📊 Total generation: {df['generation_kwh'].sum():.2f} kWh")
                print(f"📊 Total consumption: {df['consumption_kwh'].sum():.2f} kWh")
                
                # Test visualization
                try:
                    fig = plot_tod_generation_consumption_lines(
                        df=df,
                        plant_display_name=client_name,
                        start_date=start_date,
                        end_date=end_date,
                        is_interactive=True
                    )
                    
                    if fig is not None:
                        print(f"✅ {granularity} visualization created successfully")
                    else:
                        print(f"❌ {granularity} visualization failed")
                        
                except Exception as e:
                    print(f"❌ {granularity} visualization error: {e}")
                    
            else:
                print(f"⚠️ No data found for {granularity} granularity")
    
    except Exception as e:
        print(f"❌ Data fetching error: {e}")
    
    finally:
        try:
            conn.close()
            print("✅ Database connection closed")
        except:
            pass

def test_import_functions():
    """Test if all import functions work correctly"""
    
    print("\n🔍 Testing Import Functions...")
    
    try:
        # Test database imports
        from db.db_setup import get_db_connection
        from db.fetch_tod_tab_data import fetch_tod_data_with_granularity
        print("✅ Database imports successful")
        
        # Test visualization imports
        from visualizations.summary_tab_visual import plot_tod_generation_consumption_lines
        from visualizations.tod_config import SLOT_METADATA, get_slot_color_map
        print("✅ Visualization imports successful")
        
        # Test display imports
        from frontend.display_plots.summary_display import display_tod_generation_consumption_lines
        print("✅ Display imports successful")
        
        # Test slot configuration
        print(f"🎯 Available slots: {list(SLOT_METADATA.keys())}")
        slot_colors = get_slot_color_map()
        print(f"🎨 Slot colors: {slot_colors}")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting ToD Line Chart Test...")
    
    # Test imports first
    if test_import_functions():
        print("\n" + "="*50)
        test_tod_line_chart()
    else:
        print("❌ Import test failed, skipping data test")
    
    print("\n✅ Test completed!")