#!/usr/bin/env python3
"""
Example usage of the new ToD line chart functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.db_setup import get_db_connection
from db.fetch_tod_tab_data import fetch_tod_data_with_granularity
from visualizations.summary_tab_visual import plot_tod_generation_consumption_lines

def example_usage():
    """Example showing how to use the new ToD line chart functionality"""
    
    print("📊 ToD Line Chart Example Usage")
    print("="*50)
    
    # Step 1: Get database connection
    conn = get_db_connection()
    if conn is None:
        print("❌ Could not connect to database")
        return
    
    # Step 2: Define parameters
    client_name = "your_client_name"  # Replace with actual client name
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    granularity = "daily"  # Options: "daily", "60min", "15min"
    
    print(f"Client: {client_name}")
    print(f"Date Range: {start_date} to {end_date}")
    print(f"Granularity: {granularity}")
    print()
    
    try:
        # Step 3: Fetch data
        print("🔄 Fetching ToD data...")
        df = fetch_tod_data_with_granularity(
            conn=conn,
            client_name=client_name,
            start_date=start_date,
            end_date=end_date,
            granularity=granularity
        )
        
        if df.empty:
            print("⚠️ No data found for the specified parameters")
            return
            
        print(f"✅ Data fetched successfully: {len(df)} records")
        print(f"📊 Columns: {list(df.columns)}")
        print(f"🎯 Available slots: {df['slot'].unique()}")
        print()
        
        # Step 4: Create visualization
        print("🎨 Creating line chart...")
        fig = plot_tod_generation_consumption_lines(
            df=df,
            plant_display_name=client_name,
            start_date=start_date,
            end_date=end_date,
            is_interactive=True  # Set to False for matplotlib version
        )
        
        if fig is not None:
            print("✅ Line chart created successfully!")
            print("📈 Chart contains 8 lines:")
            print("   - 4 solid lines for generation (one per ToD slot)")
            print("   - 4 dashed lines for consumption (one per ToD slot)")
            print()
            
            # Step 5: Display data summary
            print("📊 Data Summary:")
            slot_summary = df.groupby('slot').agg({
                'generation_kwh': 'sum',
                'consumption_kwh': 'sum'
            }).round(2)
            
            for slot, row in slot_summary.iterrows():
                print(f"   {slot}: Gen={row['generation_kwh']} kWh, Cons={row['consumption_kwh']} kWh")
            
            print()
            print("💡 To display this in Streamlit, use:")
            print("   st.plotly_chart(fig, use_container_width=True)")
            
        else:
            print("❌ Failed to create line chart")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        conn.close()

def show_granularity_options():
    """Show the different granularity options available"""
    
    print("\n🔧 Granularity Options:")
    print("="*30)
    print("1. 'daily' - Daily aggregated data")
    print("   - Best for: Long-term trend analysis")
    print("   - Performance: Fast")
    print("   - Data points: Fewer")
    print()
    print("2. '60min' - Hourly aggregated data")
    print("   - Best for: Intraday pattern analysis")
    print("   - Performance: Medium")
    print("   - Data points: More detailed")
    print()
    print("3. '15min' - 15-minute interval data")
    print("   - Best for: Detailed analysis")
    print("   - Performance: Slower for large ranges")
    print("   - Data points: Most detailed")
    print()

def show_integration_steps():
    """Show how to integrate into existing dashboard"""
    
    print("\n🔗 Integration Steps:")
    print("="*25)
    print("1. Add import to your display file:")
    print("   from frontend.display_plots.summary_display import display_tod_generation_consumption_lines")
    print()
    print("2. Add to your Streamlit app:")
    print("   display_tod_generation_consumption_lines(client_name, start_date, end_date)")
    print()
    print("3. The function will automatically:")
    print("   - Show granularity selector")
    print("   - Display metrics")
    print("   - Create interactive line chart")
    print("   - Show explanatory text")
    print()

if __name__ == "__main__":
    print("🚀 ToD Line Chart - Example Usage")
    print("="*50)
    
    example_usage()
    show_granularity_options()
    show_integration_steps()
    
    print("\n✅ Example completed!")