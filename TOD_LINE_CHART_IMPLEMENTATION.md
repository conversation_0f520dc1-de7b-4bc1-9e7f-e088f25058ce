# ToD Generation vs Consumption Line Chart Implementation

## Overview
This implementation adds a new ToD (Time of Day) line chart to the Summary tab that displays generation and consumption data across different time slots with multiple granularity options.

## Features

### 📊 Chart Characteristics
- **8 lines total**: 4 for generation + 4 for consumption
- **4 ToD slots**: Morning Peak, Day Normal, Evening Peak, Night Off-Peak
- **Color-coded by slot**: Each slot has its own color scheme
- **Interactive**: Built with Plotly for better user experience

### ⚙️ Granularity Options
- **Daily**: Aggregated by day (fastest, overview)
- **60min**: Aggregated by hour (balanced detail/performance)  
- **15min**: 15-minute intervals (most detailed, slower)

### 🎯 Key Metrics
- Total generation/consumption by slot
- Surplus generation/demand calculations
- Data point count and date range summary

## Files Modified/Created

### 1. Database Layer (`db/fetch_tod_tab_data.py`)
- **New function**: `fetch_tod_data_with_granularity()`
- Handles different granularity queries
- Returns properly formatted data for visualization

### 2. Visualization Layer (`visualizations/summary_tab_visual.py`)
- **New function**: `plot_tod_generation_consumption_lines()`
- Creates interactive Plotly line chart
- Supports both interactive and static modes
- Handles 8 lines with proper styling

### 3. Display Layer (`frontend/display_plots/summary_display.py`)
- **New function**: `display_tod_generation_consumption_lines()`
- Streamlit UI with granularity selector
- Metrics display and explanatory text
- Error handling and loading states

### 4. Main Application (`app.py`)
- Added import for new display function
- Integrated into Summary tab
- Proper error handling and logging

## Usage

### In the Dashboard
1. Navigate to the **Summary** tab
2. Select your client/plant and date range
3. Scroll to the **ToD Generation vs Consumption Lines** section
4. Choose your desired granularity (daily/60min/15min)
5. View the interactive chart with 8 lines

### Programmatic Usage
```python
from frontend.display_plots.summary_display import display_tod_generation_consumption_lines

# Display in Streamlit
display_tod_generation_consumption_lines(client_name, start_date, end_date)
```

## Chart Explanation

### Line Types
- **Solid lines**: Generation data
- **Dashed lines**: Consumption data

### Color Scheme
- **Orange**: Morning Peak (6am-9am)
- **Blue**: Day Normal (9am-6pm)
- **Red**: Evening Peak (6pm-10pm)
- **Purple**: Night Off-Peak (10pm-6am)

### Data Points
- Each line represents one ToD slot's trend over time
- X-axis shows date/time based on selected granularity
- Y-axis shows energy values in kWh

## Testing

### Test Files Created
- `test_tod_line_chart.py`: Comprehensive functionality testing
- `example_tod_usage.py`: Usage examples and integration guide

### Test Coverage
- Database connection and data fetching
- Visualization creation (both interactive and static)
- Import functionality
- Error handling

## Performance Considerations

### Granularity Impact
- **Daily**: Fastest, suitable for long date ranges
- **60min**: Medium speed, good for weekly/monthly analysis
- **15min**: Slower, best for single day or short ranges

### Optimization Features
- Proper database indexing on datetime and slot_name
- Efficient aggregation queries
- Memory-efficient data processing

## Integration Notes

### Dependencies
- All existing dashboard dependencies (Plotly, Streamlit, pandas)
- Uses existing ToD configuration (`visualizations/tod_config.py`)
- Leverages existing database connection setup

### Backwards Compatibility
- No breaking changes to existing functionality
- Added as new feature to Summary tab
- Maintains existing chart functionality

## Future Enhancements

### Potential Additions
- Export functionality for chart data
- Comparison mode between different clients
- Statistical analysis overlay (mean, median, etc.)
- Custom time range selection within granularity

### Performance Improvements
- Data caching for repeated queries
- Async data loading for better UX
- Progressive loading for large datasets

## Troubleshooting

### Common Issues
1. **No data displayed**: Check client name and date range
2. **Slow loading**: Use coarser granularity for large date ranges
3. **Missing slots**: Verify slot_name data in database
4. **Chart not rendering**: Check console for Plotly errors

### Debug Steps
1. Run `test_tod_line_chart.py` to verify setup
2. Check database connection and data availability
3. Verify import paths and function availability
4. Test with known good data parameters

## Summary

This implementation successfully adds a comprehensive ToD line chart to the Summary tab with:
- ✅ 8 lines (4 generation + 4 consumption)
- ✅ 4 ToD slots with proper color coding
- ✅ 3 granularity options (15min, 60min, daily)
- ✅ Interactive Plotly visualization
- ✅ Comprehensive UI with metrics and explanations
- ✅ Proper error handling and logging
- ✅ Full integration with existing dashboard

The feature is production-ready and provides valuable insights into ToD-based energy patterns.